import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { addFunds } from "./balance-service";
import { getTxLookup, initializeTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity } from "./types";

const db = admin.firestore();

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

interface QuickNodeTonResponse {
  ok: boolean;
  result: TonTransaction[];
}

function getTonRpcUrl() {
  const network = process.env.TON_NETWORK ?? "mainnet";
  const rpcUrl =
    "https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c";

  if (!rpcUrl) {
    throw new Error(
      `TON_RPC_URL_${network.toUpperCase()} environment variable not set`
    );
  }

  // Ensure URL ends with / for proper path concatenation
  return rpcUrl.endsWith("/") ? rpcUrl : `${rpcUrl}/`;
}

function getMarketplaceWallet() {
  const wallet =
    process.env.TON_MARKETPLACE_WALLET ||
    "EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t";
  if (!wallet) {
    throw new Error("TON_MARKETPLACE_WALLET environment variable not set");
  }
  return wallet;
}

async function fetchTonTransactions(
  address: string,
  fromLt?: string
): Promise<TonTransaction[]> {
  const baseUrl = getTonRpcUrl();

  // Build URL parameters for QuickNode TON API
  const params = new URLSearchParams({
    address: address,
    to_lt: "0",
    archival: "true",
  });

  // Add lt parameter if provided (for pagination)
  if (fromLt && fromLt !== "0") {
    params.append("lt", fromLt);
  }

  const url = `${baseUrl}getTransactions?${params.toString()}`;

  const headers: Record<string, string> = {
    accept: "application/json",
  };

  try {
    console.log(`Fetching TON transactions from: ${url}`);
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data: QuickNodeTonResponse = await response.json();

    if (!data.ok) {
      throw new Error("TON API returned error response");
    }

    console.log(`Fetched ${data.result?.length ?? 0} transactions`);
    return data.result ?? [];
  } catch (error) {
    console.error("Error fetching TON transactions:", error);
    throw error;
  }
}

function extractTransactionInfo(
  tx: TonTransaction
): { sender: string; amount: number; message?: string } | null {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = parseInt(tx.in_msg.value) / 1000000000;

  if (amount <= 0) {
    return null;
  }

  return {
    sender: tx.in_msg.source,
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function findUserByTonWallet(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  try {
    const usersRef = db.collection("users");
    const query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
    const snapshot = await query.get();

    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  } catch (error) {
    console.error("Error finding user by TON wallet:", error);
    return null;
  }
}

async function updateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  try {
    await addFunds(userId, amount);
    console.log(`Updated balance for user ${userId}: +${amount} TON`);
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

async function processTransactions(
  transactions: TonTransaction[]
): Promise<void> {
  console.log(`Processing ${transactions.length} transactions`);

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue;
      }

      console.log(
        `Processing transaction: ${tx.transaction_id.lt}, sender: ${txInfo.sender}, amount: ${txInfo.amount} TON`
      );

      const user = await findUserByTonWallet(txInfo.sender);

      if (!user) {
        console.log(`No user found for wallet address: ${txInfo.sender}`);
        continue;
      }

      if (!user.tg_id) {
        console.log(`User ${user.id} has no tg_id, skipping balance update`);
        continue;
      }

      await updateUserBalance(user.id, txInfo.amount);

      console.log(
        `Successfully processed topup for user ${user.id} (${user.tg_id}): ${txInfo.amount} TON`
      );
    } catch (error) {
      console.error(
        `Error processing transaction ${tx.transaction_id.lt}:`,
        error
      );
    }
  }
}

export async function monitorTonTransactions(): Promise<void> {
  try {
    console.log("Starting TON transaction monitoring...");

    await initializeTxLookup();

    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id ?? "0";

    console.log(`Last checked LT: ${lastCheckedLt}`);

    const marketplaceWallet = getMarketplaceWallet();

    const transactions = await fetchTonTransactions(
      marketplaceWallet,
      lastCheckedLt
    );

    if (transactions.length === 0) {
      console.log("No new transactions found");
      return;
    }

    transactions.sort(
      (a, b) => parseInt(a.transaction_id.lt) - parseInt(b.transaction_id.lt)
    );

    await processTransactions(transactions);

    if (transactions.length > 0) {
      const latestLt = transactions[transactions.length - 1].transaction_id.lt;
      await updateTxLookup(latestLt);
      console.log(`Updated last checked LT to: ${latestLt}`);
    }

    console.log("TON transaction monitoring completed successfully");
  } catch (error) {
    console.error("Error in TON transaction monitoring:", error);
    throw error;
  }
}
