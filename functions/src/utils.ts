import { Timestamp } from "firebase-admin/firestore";

export function firebaseTimestampToDate(timestamp: Timestamp): Date {
  return timestamp.toDate();
}

export function formatDateToFirebaseTimestamp(
  date: Date | Timestamp
): Timestamp {
  if (date instanceof Timestamp) {
    return date;
  }
  return Timestamp.fromDate(date);
}

export function generateUniqueId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
