import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { addFunds } from "./balance-service";
import { getTxLookup, initializeTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity } from "./types";


function extractRawAddress(address: string): string | null {
  if (!address || address.length < 48) {
    return null;
  }


  return address.substring(2, address.length - 2);
}


async function findUserByTonWalletFlexible(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  const db = admin.firestore();
  const usersRef = db.collection("users");

  console.log(`Looking for user with TON address: ${tonWalletAddress}`);

  // First try exact match
  let query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
  let snapshot = await query.get();

  if (!snapshot.empty) {
    const doc = snapshot.docs[0];
    console.log(`Found exact match for address: ${tonWalletAddress}`);
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  }

  const rawAddress = extractRawAddress(tonWalletAddress);
  if (!rawAddress) {
    console.log(`Invalid address format: ${tonWalletAddress}`);
    return null;
  }

  console.log(
    `No exact match found, searching by raw address part: ${rawAddress}`
  );

  const allUsersSnapshot = await usersRef
    .where("ton_wallet_address", "!=", "")
    .get();

  for (const doc of allUsersSnapshot.docs) {
    const userData = doc.data() as UserEntity;
    const userAddress = userData.ton_wallet_address;

    if (userAddress) {
      const userRawAddress = extractRawAddress(userAddress);
      if (userRawAddress === rawAddress) {
        console.log(
          `Found matching user: ${userAddress} matches ${tonWalletAddress} (same raw address)`
        );
        return {
          ...userData,
          id: doc.id,
        } as UserEntity;
      }
    }
  }

  console.log(`No user found for address: ${tonWalletAddress}`);
  return null;
}


function filterNewTransactions(
  transactions: TonTransaction[],
  lastCheckedLt: string
): TonTransaction[] {
  if (lastCheckedLt === "0") {
    console.log(
      "No previous transactions processed, returning all transactions"
    );
    return transactions;
  }

  const filtered = transactions.filter((tx) => {
    const txLt = parseInt(tx.transaction_id.lt);
    const lastLt = parseInt(lastCheckedLt);
    const isNew = txLt > lastLt;
    console.log(
      `Transaction LT: ${txLt}, Last checked LT: ${lastLt}, Is new: ${isNew}`
    );
    return isNew;
  });

  console.log(
    `Filtered ${transactions.length} transactions down to ${filtered.length} new ones`
  );
  return filtered;
}

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

interface QuickNodeTonResponse {
  ok: boolean;
  result: TonTransaction[];
}

function getTonRpcUrl() {
  const network = process.env.TON_NETWORK ?? "mainnet";
  const rpcUrl =
    "https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c";

  if (!rpcUrl) {
    throw new Error(
      `TON_RPC_URL_${network.toUpperCase()} environment variable not set`
    );
  }

  // Ensure URL ends with / for proper path concatenation
  return rpcUrl.endsWith("/") ? rpcUrl : `${rpcUrl}/`;
}

function getMarketplaceWallet() {
  const wallet =
    process.env.TON_MARKETPLACE_WALLET ??
    "UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI";
  if (!wallet) {
    throw new Error("TON_MARKETPLACE_WALLET environment variable not set");
  }
  return wallet;
}

async function fetchTonTransactions(
  address: string,
  fromLt?: string
): Promise<TonTransaction[]> {
  const baseUrl = getTonRpcUrl();

  // Build URL parameters for QuickNode TON API
  const params = new URLSearchParams({
    address: address,
    to_lt: "0",
    archival: "true",
  });

  // Add lt parameter if provided (for pagination) - but we're not using this anymore
  // We fetch all transactions and filter them in the application logic
  if (fromLt && fromLt !== "0") {
    params.append("lt", fromLt);
  }

  const url = `${baseUrl}getTransactions?${params.toString()}`;

  const headers: Record<string, string> = {
    accept: "application/json",
  };

  try {
    console.log(`Fetching TON transactions from: ${url}`);
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data: QuickNodeTonResponse = await response.json();

    if (!data.ok) {
      throw new Error("TON API returned error response");
    }

    console.log(`Fetched ${data.result?.length ?? 0} transactions`);
    return data.result ?? [];
  } catch (error) {
    console.error("Error fetching TON transactions:", error);
    throw error;
  }
}

function extractTransactionInfo(
  tx: TonTransaction
): { sender: string; amount: number; message?: string } | null {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = parseInt(tx.in_msg.value) / 1000000000;

  if (amount <= 0) {
    return null;
  }

  const originalSender = tx.in_msg.source;

  console.log(
    `Transaction from address: ${originalSender}, amount: ${amount} TON`
  );

  return {
    sender: originalSender, // Keep original address, flexible matching will handle it
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function updateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  try {
    await addFunds(userId, amount);
    console.log(`Updated balance for user ${userId}: +${amount} TON`);
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

async function processTransactions(
  transactions: TonTransaction[]
): Promise<void> {
  console.log(`Processing ${transactions.length} transactions`);

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue;
      }

      console.log(
        `Processing transaction: ${tx.transaction_id.lt}, sender: ${txInfo.sender}, amount: ${txInfo.amount} TON`
      );

      const user = await findUserByTonWalletFlexible(txInfo.sender);

      if (!user) {
        console.log(`No user found for wallet address: ${txInfo.sender}`);
        continue;
      }

      if (!user.tg_id) {
        console.log(`User ${user.id} has no tg_id, skipping balance update`);
        continue;
      }

      await updateUserBalance(user.id, txInfo.amount);

      console.log(
        `Successfully processed topup for user ${user.id} (${user.tg_id}): ${txInfo.amount} TON`
      );
    } catch (error) {
      console.error(
        `Error processing transaction ${tx.transaction_id.lt}:`,
        error
      );
    }
  }
}

export async function monitorTonTransactions(): Promise<void> {
  try {
    console.log("Starting TON transaction monitoring...");

    await initializeTxLookup();

    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id ?? "0";

    console.log(`Last checked LT: ${lastCheckedLt}`);

    const marketplaceWallet = getMarketplaceWallet();

    // Fetch all transactions (don't use fromLt parameter to get all available)
    const allTransactions = await fetchTonTransactions(marketplaceWallet);

    if (allTransactions.length === 0) {
      console.log("No transactions found");
      return;
    }

    // Filter out already processed transactions
    const newTransactions = filterNewTransactions(
      allTransactions,
      lastCheckedLt
    );

    if (newTransactions.length === 0) {
      console.log("No new transactions to process");
      return;
    }

    console.log(`Found ${newTransactions.length} new transactions to process`);

    // Sort transactions by LT (oldest first)
    newTransactions.sort(
      (a, b) => parseInt(a.transaction_id.lt) - parseInt(b.transaction_id.lt)
    );

    await processTransactions(newTransactions);

    if (newTransactions.length > 0) {
      const latestLt =
        newTransactions[newTransactions.length - 1].transaction_id.lt;
      await updateTxLookup(latestLt);
      console.log(`Updated last checked LT to: ${latestLt}`);
    }

    console.log("TON transaction monitoring completed successfully");
  } catch (error) {
    console.error("Error in TON transaction monitoring:", error);
    throw error;
  }
}
